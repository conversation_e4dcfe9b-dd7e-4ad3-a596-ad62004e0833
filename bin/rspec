#!/usr/bin/env ruby
require "rubygems"

# Ensure we're using the correct bundler version
begin
  require "bundler/version"
  if Gem::Version.new(Bundler::VERSION) < Gem::Version.new('2.6.8')
    warn "This project requires Bundler 2.6.8 or higher. Please run 'gem install bundler:2.6.8'"
    exit 1
  end
rescue LoadError
  warn "B<PERSON><PERSON> not found. Please run 'gem install bundler:2.6.8'"
  exit 1
end

require "bundler/setup"

load Gem.bin_path("rspec-core", "rspec")
