# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_05_25_091420) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "approval_steps", force: :cascade do |t|
    t.bigint "approval_workflow_id", null: false
    t.integer "sequence", null: false
    t.string "name", null: false
    t.string "approval_type", default: "any", null: false
    t.string "dynamic_approver_method", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "condition"
    t.boolean "skip_if_no_approvers", default: false
    t.json "static_approver_ids"
    t.text "selection_explanation"
    t.index ["approval_workflow_id", "sequence"], name: "index_approval_steps_on_approval_workflow_id_and_sequence", unique: true
    t.index ["approval_workflow_id"], name: "index_approval_steps_on_approval_workflow_id"
  end

  create_table "approval_workflows", force: :cascade do |t|
    t.string "name", null: false
    t.string "action", null: false
    t.string "subject_class", null: false
    t.string "system_name", null: false
    t.text "description"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "condition"
    t.json "required_context_keys"
    t.string "empty_steps_behavior", default: "auto_approve"
    t.index ["action", "subject_class", "system_name"], name: "idx_on_action_subject_class_system_name_5521282da6", unique: true
    t.index ["name"], name: "index_approval_workflows_on_name", unique: true
  end

  create_table "permissions", force: :cascade do |t|
    t.string "name"
    t.string "action"
    t.string "subject_class"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "system_name", default: "global", null: false
    t.index ["system_name"], name: "index_permissions_on_system_name"
  end

  create_table "projects", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.boolean "status", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "role_permissions", force: :cascade do |t|
    t.bigint "role_id", null: false
    t.bigint "permission_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["permission_id"], name: "index_role_permissions_on_permission_id"
    t.index ["role_id"], name: "index_role_permissions_on_role_id"
  end

  create_table "roles", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "scope", default: false, null: false
    t.integer "level", default: 10
    t.index ["level"], name: "index_roles_on_level"
    t.index ["name"], name: "index_roles_on_name", unique: true
    t.index ["scope"], name: "index_roles_on_scope"
  end

  create_table "user_roles", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "role_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "project_id"
    t.boolean "is_default", default: false
    t.index ["project_id"], name: "index_user_roles_on_project_id"
    t.index ["role_id"], name: "index_user_roles_on_role_id"
    t.index ["user_id", "is_default"], name: "index_user_roles_on_user_id_and_is_default", unique: true, where: "(is_default = true)"
    t.index ["user_id"], name: "index_user_roles_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.boolean "status", default: true
    t.boolean "global", default: false, null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["global"], name: "index_users_on_global"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "approval_steps", "approval_workflows"
  add_foreign_key "role_permissions", "permissions"
  add_foreign_key "role_permissions", "roles"
  add_foreign_key "user_roles", "projects"
  add_foreign_key "user_roles", "roles"
  add_foreign_key "user_roles", "users"
end
