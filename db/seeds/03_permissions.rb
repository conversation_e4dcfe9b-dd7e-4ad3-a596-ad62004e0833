# Define permissions per subsystem
permissions = [
  # 🔹 AtharCM (Case Manager Subsystem)
  { name: "Manage Case Managers", action: "manage", subject_class: "CaseManager", system_name: "case_manager" },
  { name: "Read Case Managers", action: "read", subject_class: "CaseManager", system_name: "case_manager" },
  { name: "Create Case Manager", action: "create", subject_class: "CaseManager", system_name: "case_manager" },
  { name: "Update Case Manager", action: "update", subject_class: "CaseManager", system_name: "case_manager" },
  { name: "Delete Case Manager", action: "destroy", subject_class: "CaseManager", system_name: "case_manager" },

  { name: "Manage Cases", action: "manage", subject_class: "Case", system_name: "case_manager" },
  { name: "Read Cases", action: "read", subject_class: "Case", system_name: "case_manager" },
  { name: "Create Case", action: "create", subject_class: "Case", system_name: "case_manager" },
  { name: "Update Case", action: "update", subject_class: "Case", system_name: "case_manager" },
  { name: "Delete Case", action: "destroy", subject_class: "Case", system_name: "case_manager" },

  { name: "Manage Case Plans", action: "manage", subject_class: "CasePlan", system_name: "case_manager" },
  { name: "Read Case Plans", action: "read", subject_class: "CasePlan", system_name: "case_manager" },
  { name: "Create Case Plan", action: "create", subject_class: "CasePlan", system_name: "case_manager" },
  { name: "Update Case Plan", action: "update", subject_class: "CasePlan", system_name: "case_manager" },
  { name: "Delete Case Plan", action: "destroy", subject_class: "CasePlan", system_name: "case_manager" },

  { name: "Manage Beneficiaries", action: "manage", subject_class: "Beneficiary", system_name: "case_manager" },
  { name: "Read Beneficiaries", action: "read", subject_class: "Beneficiary", system_name: "case_manager" },
  { name: "Create Beneficiary", action: "create", subject_class: "Beneficiary", system_name: "case_manager" },
  { name: "Update Beneficiary", action: "update", subject_class: "Beneficiary", system_name: "case_manager" },
  { name: "Delete Beneficiary", action: "destroy", subject_class: "Beneficiary", system_name: "case_manager" },

  { name: "Manage Assessments", action: "manage", subject_class: "Assessment", system_name: "case_manager" },
  { name: "Read Assessments", action: "read", subject_class: "Assessment", system_name: "case_manager" },
  { name: "Create Assessment", action: "create", subject_class: "Assessment", system_name: "case_manager" },
  { name: "Update Assessment", action: "update", subject_class: "Assessment", system_name: "case_manager" },
  { name: "Delete Assessment", action: "destroy", subject_class: "Assessment", system_name: "case_manager" },

  { name: "Manage Sessions", action: "manage", subject_class: "Session", system_name: "case_manager" },
  { name: "Read Sessions", action: "read", subject_class: "Session", system_name: "case_manager" },
  { name: "Create Session", action: "create", subject_class: "Session", system_name: "case_manager" },
  { name: "Update Session", action: "update", subject_class: "Session", system_name: "case_manager" },
  { name: "Delete Session", action: "destroy", subject_class: "Session", system_name: "case_manager" },

  { name: "Manage Services", action: "manage", subject_class: "Service", system_name: "case_manager" },
  { name: "Read Services", action: "read", subject_class: "Service", system_name: "case_manager" },
  { name: "Create Service", action: "create", subject_class: "Service", system_name: "case_manager" },
  { name: "Update Service", action: "update", subject_class: "Service", system_name: "case_manager" },
  { name: "Delete Service", action: "destroy", subject_class: "Service", system_name: "case_manager" },

  { name: "Generate Mental Health Reports", action: "generate", subject_class: "Report", system_name: "case_manager" },
  { name: "Export Mental Health Reports", action: "export", subject_class: "Report", system_name: "case_manager" },

  # 🔹 AtharPeople (HR Management Subsystem)
  { name: "Manage Employees", action: "manage", subject_class: "Employee", system_name: "people" },
  { name: "Read Employees", action: "read", subject_class: "Employee", system_name: "people" },
  { name: "Create Employee", action: "create", subject_class: "Employee", system_name: "people" },
  { name: "Update Employee", action: "update", subject_class: "Employee", system_name: "people" },
  { name: "Delete Employee", action: "destroy", subject_class: "Employee", system_name: "people" },
  { name: "Read Own Employee", action: "read_own", subject_class: "Employee", system_name: "people" },
  { name: "Update Own Employee", action: "update_own", subject_class: "Employee", system_name: "people" },

  { name: "Manage Payroll", action: "manage", subject_class: "Payroll", system_name: "people" },
  { name: "Read Payroll", action: "read", subject_class: "Payroll", system_name: "people" },
  { name: "Create Payroll", action: "create", subject_class: "Payroll", system_name: "people" },
  { name: "Update Payroll", action: "update", subject_class: "Payroll", system_name: "people" },
  { name: "Delete Payroll", action: "destroy", subject_class: "Payroll", system_name: "people" },

  # Leave Management Permissions
  { name: "Manage Leaves", action: "manage", subject_class: "Leave", system_name: "people" },
  { name: "Read Leaves", action: "read", subject_class: "Leave", system_name: "people" },
  { name: "Create Leave", action: "create", subject_class: "Leave", system_name: "people" },
  { name: "Update Leave", action: "update", subject_class: "Leave", system_name: "people" },
  { name: "Submit Leave", action: "submit", subject_class: "Leave", system_name: "people" },
  { name: "Withdraw Leave", action: "withdraw", subject_class: "Leave", system_name: "people" },
  { name: "Approve Leave", action: "approve", subject_class: "Leave", system_name: "people" },
  { name: "Reject Leave", action: "reject", subject_class: "Leave", system_name: "people" },
  { name: "Read Own Leaves", action: "read_own", subject_class: "Leave", system_name: "people" },
  { name: "Manage Own Leaves", action: "manage_own", subject_class: "Leave", system_name: "people" },

  # Attendance Management Permissions
  { name: "Manage Attendance Events", action: "manage", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Read Attendance Events", action: "read", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Create Attendance Event", action: "create", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Update Attendance Event", action: "update", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Delete Attendance Event", action: "destroy", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Record Own Attendance", action: "record", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Read Own Attendance Events", action: "read_own", subject_class: "AttendanceEvent", system_name: "people" },

  # Attendance Device Management Permissions
  { name: "Manage Attendance Devices", action: "manage", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Read Attendance Devices", action: "read", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Create Attendance Device", action: "create", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Update Attendance Device", action: "update", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Delete Attendance Device", action: "destroy", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Test Device Connection", action: "test_connection", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Sync Device Data", action: "sync", subject_class: "AttendanceDevice", system_name: "people" },

  # Attendance Sync Log Permissions
  { name: "Manage Attendance Sync Logs", action: "manage", subject_class: "AttendanceSyncLog", system_name: "people" },
  { name: "Read Attendance Sync Logs", action: "read", subject_class: "AttendanceSyncLog", system_name: "people" },
  { name: "Cleanup Sync Logs", action: "cleanup", subject_class: "AttendanceSyncLog", system_name: "people" },

  # Settings Permissions
  { name: "Manage Settings", action: "manage", subject_class: "setting", system_name: "people" },
  { name: "Read Settings", action: "read", subject_class: "setting", system_name: "people" },
  { name: "Create Settings", action: "create", subject_class: "setting", system_name: "people" },
  { name: "Update Settings", action: "update", subject_class: "setting", system_name: "people" },

  { name: "Generate HR Reports", action: "generate", subject_class: "HRReport", system_name: "people" },
  { name: "Export HR Reports", action: "export", subject_class: "HRReport", system_name: "people" },

  # 🔹 AtharProcure (Procurement Management Subsystem)
  { name: "Manage Procurement Requests", action: "manage", subject_class: "ProcurementRequest", system_name: "procure" },
  { name: "Read Procurement Requests", action: "read", subject_class: "ProcurementRequest", system_name: "procure" },
  { name: "Create Procurement Request", action: "create", subject_class: "ProcurementRequest", system_name: "procure" },
  { name: "Update Procurement Request", action: "update", subject_class: "ProcurementRequest", system_name: "procure" },
  { name: "Delete Procurement Request", action: "destroy", subject_class: "ProcurementRequest", system_name: "procure" },

  { name: "Approve Procurement", action: "approve", subject_class: "ProcurementRequest", system_name: "procure" },
  { name: "Reject Procurement", action: "reject", subject_class: "ProcurementRequest", system_name: "procure" },

  { name: "Generate Procurement Reports", action: "generate", subject_class: "ProcurementReport", system_name: "procure" },
  { name: "Export Procurement Reports", action: "export", subject_class: "ProcurementReport", system_name: "procure" },

  # 🔹 System-Wide Permissions
  { name: "Manage Users", action: "manage", subject_class: "User", system_name: "core" },
  { name: "Read Users", action: "read", subject_class: "User", system_name: "core" },
  { name: "Create User", action: "create", subject_class: "User", system_name: "core" },
  { name: "Update User", action: "update", subject_class: "User", system_name: "core" },
  { name: "Delete User", action: "destroy", subject_class: "User", system_name: "core" },
  { name: "Read Own User", action: "read_own", subject_class: "User", system_name: "core" },
  { name: "Manage Own User", action: "manage_own", subject_class: "User", system_name: "core" },

  { name: "Manage Roles", action: "manage", subject_class: "Role", system_name: "core" },
  { name: "Read Roles", action: "read", subject_class: "Role", system_name: "core" },
  { name: "Create Role", action: "create", subject_class: "Role", system_name: "core" },
  { name: "Update Role", action: "update", subject_class: "Role", system_name: "core" },
  { name: "Destroy Role", action: "destroy", subject_class: "Role", system_name: "core" },

  { name: "Manage Projects", action: "manage", subject_class: "Project", system_name: "core" },
  { name: "Read Projects", action: "read", subject_class: "Project", system_name: "core" },
  { name: "Create Project", action: "create", subject_class: "Project", system_name: "core" },
  { name: "Update Project", action: "update", subject_class: "Project", system_name: "core" },
  { name: "Destroy Project", action: "destroy", subject_class: "Project", system_name: "core" },

  { name: "Assign Permissions", action: "assign", subject_class: "Permission", system_name: "core" },

  # Approval System Permissions
  { name: "Manage Approval Requests", action: "manage", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Read Approval Requests", action: "read", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Read Own Approval Requests", action: "read_own", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Approve Approval Request", action: "approve", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Reject Approval Request", action: "reject", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Cancel Approval Request", action: "cancel", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Cancel Own Approval Request", action: "cancel_own", subject_class: "ApprovalRequest", system_name: "core" },

  # People service approval permissions
  { name: "Read Approval Requests", action: "read", subject_class: "ApprovalRequest", system_name: "people" },
  { name: "Read Own Approval Requests", action: "read_own", subject_class: "ApprovalRequest", system_name: "people" },
  { name: "Approve Approval Request", action: "approve", subject_class: "ApprovalRequest", system_name: "people" },
  { name: "Reject Approval Request", action: "reject", subject_class: "ApprovalRequest", system_name: "people" },
  { name: "Cancel Approval Request", action: "cancel", subject_class: "ApprovalRequest", system_name: "people" },
  { name: "Cancel Own Approval Request", action: "cancel_own", subject_class: "ApprovalRequest", system_name: "people" },

  # Procure service approval permissions
  { name: "Read Approval Requests", action: "read", subject_class: "ApprovalRequest", system_name: "procure" },
  { name: "Read Own Approval Requests", action: "read_own", subject_class: "ApprovalRequest", system_name: "procure" },
  { name: "Approve Approval Request", action: "approve", subject_class: "ApprovalRequest", system_name: "procure" },
  { name: "Reject Approval Request", action: "reject", subject_class: "ApprovalRequest", system_name: "procure" },
  { name: "Cancel Approval Request", action: "cancel", subject_class: "ApprovalRequest", system_name: "procure" },
  { name: "Cancel Own Approval Request", action: "cancel_own", subject_class: "ApprovalRequest", system_name: "procure" },

  # Case Manager service approval permissions
  { name: "Read Approval Requests", action: "read", subject_class: "ApprovalRequest", system_name: "case_manager" },
  { name: "Read Own Approval Requests", action: "read_own", subject_class: "ApprovalRequest", system_name: "case_manager" },
  { name: "Approve Approval Request", action: "approve", subject_class: "ApprovalRequest", system_name: "case_manager" },
  { name: "Reject Approval Request", action: "reject", subject_class: "ApprovalRequest", system_name: "case_manager" },
  { name: "Cancel Approval Request", action: "cancel", subject_class: "ApprovalRequest", system_name: "case_manager" },
  { name: "Cancel Own Approval Request", action: "cancel_own", subject_class: "ApprovalRequest", system_name: "case_manager" }
]

# Seed the permissions into the database
permissions.each do |perm|
  Permission.find_or_create_by!(
    name: perm[:name],
    action: perm[:action],
    subject_class: perm[:subject_class],
    system_name: perm[:system_name]
  )
end

puts "✅ Permissions seeded successfully!"
