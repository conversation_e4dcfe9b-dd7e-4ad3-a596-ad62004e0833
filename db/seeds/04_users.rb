# Sample users with project roles
users = [
  {
    name: "Super Admin User",
    email: "<EMAIL>",
    encrypted_password: "password",
    global: true,
    project_roles: [
      { project: nil, role: "super_admin" }
    ]
  },
  {
    name: "Admin User",
    email: "<EMAIL>",
    encrypted_password: "password",
    global: true,
    project_roles: [
      { project: nil, role: "admin" }
    ]
  },
  {
    name: "Supervisor User",
    email: "<EMAIL>",
    encrypted_password: "password",
    project_roles: [
      { project: $masahat_aminah_project, role: "supervisor" },
      { project: $tamkeen_project, role: "project_manager" }
    ]
  },
  {
    name: "Case Manager User",
    email: "<EMAIL>",
    encrypted_password: "password",
    project_roles: [
      { project: $masahat_aminah_project, role: "case_manager" }
    ]
  },
  {
    name: "Project Manager User",
    email: "<EMAIL>",
    encrypted_password: "password",
    project_roles: [
      { project: $dare_to_care_project, role: "project_manager" },
      { project: $good_neighbors_project, role: "supervisor" }
    ]
  },
  {
    name: "Employee User",
    email: "<EMAIL>",
    encrypted_password: "password",
    project_roles: [
      { project: $tamkeen_project, role: "employee" },
      { project: $dare_to_care_project, role: "employee" }
    ]
  },
  {
    name: "Financial Manager",
    email: "<EMAIL>",
    encrypted_password: "password",
    global: true,
    project_roles: [
      { project: nil, role: "financial_manager" }
    ]
  },
  {
    name: "Accountant User",
    email: "<EMAIL>",
    encrypted_password: "password",
    global: true,
    project_roles: [
      { project: nil, role: "accountant" }
    ]
  },
  {
    name: "HR Manager User",
    email: "<EMAIL>",
    encrypted_password: "password",
    global: true,
    project_roles: [
      { project: nil, role: "hr_manager" }
    ]
  },
  {
    name: "HR Officer User",
    email: "<EMAIL>",
    encrypted_password: "password",
    global: true,
    project_roles: [
      { project: nil, role: "hr_officer" }
    ]
  },
  {
    name: "Procurement Manager",
    email: "<EMAIL>",
    encrypted_password: "password",
    project_roles: [
      { project: $default_project, role: "procurement_manager" }
    ]
  },
  {
    name: "Procurement Officer",
    email: "<EMAIL>",
    encrypted_password: "password",
    project_roles: [
      { project: $default_project, role: "procurement_officer" }
    ]
  }
]

# Create users and assign project roles
users.each do |user_data|
  user = User.find_or_create_by!(email: user_data[:email]) do |u|
    u.name = user_data[:name]
    u.password = user_data[:encrypted_password]
    u.status = 'active' # Active by default
    u.global = user_data[:global] || false
  end

  # Set default project (first project in the list)
  default_project = nil

  # Assign roles for each project
  user_data[:project_roles].each_with_index do |project_role, index|
    role_name = project_role[:role]
    project = project_role[:project]

    # Set the first project as the default project for this user
    default_project = project if index == 0 && project

    user.add_role(role_name, project)

    # If the role is global, make sure the user is also global
    role = Role.find_by!(name: role_name)
    if role.global_role? && !user.global
      user.update!(global: true)
    end
  end

  # Set the first project as the default for this user
  if default_project
    user_role = user.user_roles.find_by!(project: default_project)
    user_role.update!(is_default: true)
  end
end

puts "✅ Users seeded successfully!"
