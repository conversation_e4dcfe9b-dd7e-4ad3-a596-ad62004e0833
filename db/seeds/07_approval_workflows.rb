puts "Creating approval workflows..."

# Procurement Request Workflow
procurement_workflow = ApprovalWorkflow.find_or_create_by!(
  name: "Procurement Request Approval",
  action: "buy_new_item",
  subject_class: "ProcurementRequest",
  system_name: "procure"
) do |workflow|
  workflow.description = "Approval workflow for procurement requests"
  workflow.active = true
end

# Step 1: Project Manager Approval
pm_step = procurement_workflow.approval_steps.find_or_create_by!(sequence: 1) do |step|
  step.name = "Project Manager Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "project_managers"
  step.description = "Approval from the project manager"
end

# Step 2: Procurement Officer/Manager Approval
procurement_step = procurement_workflow.approval_steps.find_or_create_by!(sequence: 2) do |step|
  step.name = "Procurement Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "procurement_approvers"
  step.description = "Approval from procurement officer or manager"
end

# Step 3: HR Approval
hr_step = procurement_workflow.approval_steps.find_or_create_by!(sequence: 3) do |step|
  step.name = "HR Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "hr_approvers"
  step.description = "Approval from HR officer or manager"
end

# Step 4: Financial Approval
finance_step = procurement_workflow.approval_steps.find_or_create_by!(sequence: 4) do |step|
  step.name = "Financial Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "financial_approvers"
  step.description = "Approval from accountant or financial manager"
end

# Advanced Leave Approval Workflow
advanced_leave_workflow = ApprovalWorkflow.find_or_create_by!(
  name: "Leave Approval",
  action: "request_leave",
  subject_class: "Leave",
  system_name: "people"
) do |workflow|
  workflow.description = "Advanced approval workflow for leave requests with context and hierarchy"
  workflow.active = true
  workflow.condition = "days.to_i > 0"
  workflow.required_context_keys = %w[leave_type days priority]
  workflow.empty_steps_behavior = "error"
end

# Step 1: Manager Approval
advanced_manager_step = advanced_leave_workflow.approval_steps.find_or_create_by!(sequence: 1) do |step|
  step.name = "Manager Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "direct_manager"
  step.description = "Approval from the direct (project) manager"
  step.condition = "true" # Always include this step
  step.skip_if_no_approvers = true # Skip if no manager is found, will fall back to Admin step
end

# Step 2: HR Approval based on leave type
advanced_hr_step = advanced_leave_workflow.approval_steps.find_or_create_by!(sequence: 2) do |step|
  step.name = "HR Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "leave_type_based_hr_approvers"
  step.description = "Approval from HR based on leave type"
  step.condition = "true" # Always include this step
  step.skip_if_no_approvers = false
end

# Step 3: Admin Approval - only included if no approvers were found for Step 1
# or if the leave is long duration or high priority
advanced_admin_step = advanced_leave_workflow.approval_steps.find_or_create_by!(sequence: 3) do |step|
  step.name = "Admin Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "admin_users"
  step.description = "Approval from admin when no manager is available or for special cases"
  step.condition = "previous_steps[1][:approvers].empty? || days.to_i > 14 || priority == 'high'"
  step.skip_if_no_approvers = false
end

# Context-aware Procurement Workflow
context_procurement_workflow = ApprovalWorkflow.find_or_create_by!(
  name: "Context-Aware Procurement Approval",
  action: "buy_new_item_context",
  subject_class: "ProcurementRequest",
  system_name: "procure"
) do |workflow|
  workflow.description = "Approval workflow for procurement requests with context-based conditions"
  workflow.active = true
  workflow.condition = "amount.to_f > 0"
  workflow.required_context_keys = %w[amount priority]
  workflow.empty_steps_behavior = "auto_approve"
end

# Step 1: Project Manager Approval
context_pm_step = context_procurement_workflow.approval_steps.find_or_create_by!(sequence: 1) do |step|
  step.name = "Project Manager Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "project_managers"
  step.description = "Approval from the project manager"
  step.condition = "true" # Always include this step
  step.skip_if_no_approvers = true
end

# Step 2: Procurement Officer/Manager Approval
context_procurement_step = context_procurement_workflow.approval_steps.find_or_create_by!(sequence: 2) do |step|
  step.name = "Procurement Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "procurement_approvers"
  step.description = "Approval from procurement officer or manager"
  step.condition = "amount.to_f > 1000" # Only for amounts > 1000
  step.skip_if_no_approvers = true
end

# Step 3: Financial Approval based on amount
context_finance_step = context_procurement_workflow.approval_steps.find_or_create_by!(sequence: 3) do |step|
  step.name = "Financial Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "amount_based_financial_approvers"
  step.description = "Approval from financial manager or accountant based on amount"
  step.condition = "amount.to_f > 500" # Only for amounts > 500
  step.skip_if_no_approvers = true
end

# Step 4: Admin Approval for high priority
context_admin_step = context_procurement_workflow.approval_steps.find_or_create_by!(sequence: 4) do |step|
  step.name = "Admin Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "admin_users"
  step.description = "Approval from admin for high priority requests"
  step.condition = "priority == 'high'" # Only for high priority
  step.skip_if_no_approvers = true
end

# Level-based Procurement Workflow
level_procurement_workflow = ApprovalWorkflow.find_or_create_by!(
  name: "Level-Based Procurement Approval",
  action: "buy_new_item_level",
  subject_class: "ProcurementRequest",
  system_name: "procure"
) do |workflow|
  workflow.description = "Approval workflow with level-based routing"
  workflow.active = true
  workflow.condition = "amount.to_f > 0"
  workflow.required_context_keys = [ "amount" ]
  workflow.empty_steps_behavior = "auto_approve"
end

# Step 1: Project Manager Level Approval (Level 20)
level_pm_step = level_procurement_workflow.approval_steps.find_or_create_by!(sequence: 1) do |step|
  step.name = "Project Manager Level Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "approvers_at_level"
  step.description = "Approval from users at project manager level (20)"
  step.condition = "true" # Always include this step
  step.skip_if_no_approvers = true
  step.static_approver_ids = [] # Use dynamic resolution
end

# Step 2: Department Head Level Approval (Level 30)
level_dept_step = level_procurement_workflow.approval_steps.find_or_create_by!(sequence: 2) do |step|
  step.name = "Department Head Level Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "approvers_at_level"
  step.description = "Approval from users at department head level (30)"
  step.condition = "amount.to_f > 1000" # Only for amounts > 1000
  step.skip_if_no_approvers = true
  step.static_approver_ids = [] # Use dynamic resolution
end

# Step 3: Admin Level Approval (Level 40)
level_admin_step = level_procurement_workflow.approval_steps.find_or_create_by!(sequence: 3) do |step|
  step.name = "Admin Level Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "approvers_at_level"
  step.description = "Approval from users at admin level (40)"
  step.condition = "amount.to_f > 5000" # Only for amounts > 5000
  step.skip_if_no_approvers = true
  step.static_approver_ids = [] # Use dynamic resolution
end

# Threshold-based Procurement Workflow
threshold_workflow = ApprovalWorkflow.find_or_create_by!(
  name: "Threshold-Based Procurement Approval",
  action: "buy_new_item_threshold",
  subject_class: "ProcurementRequest",
  system_name: "procure"
) do |workflow|
  workflow.description = "Approval workflow with threshold-based routing"
  workflow.active = true
  workflow.condition = "amount.to_f > 0"
  workflow.required_context_keys = [ "amount" ]
  workflow.empty_steps_behavior = "auto_approve"
end

# Single step that uses threshold_based_approvers
threshold_step = threshold_workflow.approval_steps.find_or_create_by!(sequence: 1) do |step|
  step.name = "Threshold-Based Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "threshold_based_approvers"
  step.description = "Approval from users based on amount thresholds"
  step.condition = "true" # Always include this step
  step.skip_if_no_approvers = false
  step.static_approver_ids = [] # Use dynamic resolution
end

# Salary Calculation Approval Workflow
salary_workflow = ApprovalWorkflow.find_or_create_by!(
  name: "Salary Calculation Approval",
  action: "approve_salary_calculation",
  subject_class: "SalaryCalculation",
  system_name: "people"
) do |workflow|
  workflow.description = "Approval workflow for salary calculations"
  workflow.active = true
  workflow.required_context_keys = %w[period gross_salary net_salary priority]
  workflow.empty_steps_behavior = "error"
end

# Step 1: Financial Manager Approval
salary_fm_step = salary_workflow.approval_steps.find_or_create_by!(sequence: 1) do |step|
  step.name = "Financial Manager Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "financial_managers_only"
  step.description = "Approval from financial manager"
  step.condition = "true" # Always include this step
  step.skip_if_no_approvers = false
end

# Step 2: Admin Approval for high amounts
salary_admin_step = salary_workflow.approval_steps.find_or_create_by!(sequence: 2) do |step|
  step.name = "Admin Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "admin_users"
  step.description = "Approval from admin for high value salary calculations"
  step.condition = "priority == 'high'" # Only for high priority
  step.skip_if_no_approvers = false
end

puts "✅ Approval workflows created!"
