# Add salary-related permissions
puts "Adding salary-related permissions..."

# Define new salary permissions
salary_permissions = [
  # Salary Package Permissions
  { name: "Manage Salary Packages", action: "manage", subject_class: "SalaryPackage", system_name: "people" },
  { name: "Read Salary Packages", action: "read", subject_class: "SalaryPackage", system_name: "people" },
  { name: "Read Own Salary Package", action: "read_own", subject_class: "SalaryPackage", system_name: "people" },

  # Salary Calculation Permissions
  { name: "Read Salary Calculations", action: "read", subject_class: "SalaryCalculation", system_name: "people" },
  { name: "Read Own Salary Calculations", action: "read_own", subject_class: "SalaryCalculation", system_name: "people" },
  { name: "Calculate Salary", action: "calculate", subject_class: "SalaryCalculation", system_name: "people" },
  { name: "Update Salary Calculations", action: "update", subject_class: "SalaryCalculation", system_name: "people" },
  { name: "Submit Salary Calculations", action: "submit", subject_class: "SalaryCalculation", system_name: "people" },
  { name: "Approve Salary Calculations", action: "approve", subject_class: "SalaryCalculation", system_name: "people" },

  # Configuration Permissions
  { name: "Manage Tax Configurations", action: "manage", subject_class: "TaxConfig", system_name: "people" },
  { name: "Manage Social Security Configurations", action: "manage", subject_class: "SocialSecurityConfig", system_name: "people" }
]

# Create the permissions
salary_permissions.each do |perm|
  Permission.find_or_create_by!(
    name: perm[:name],
    action: perm[:action],
    subject_class: perm[:subject_class],
    system_name: perm[:system_name]
  )
end

# Update role permissions for salary-related features
role_salary_permissions = {
  "super_admin" => Permission.where(subject_class: %w[SalaryPackage SalaryCalculation TaxConfig SocialSecurityConfig]),
  "admin" => Permission.where(subject_class: %w[SalaryPackage SalaryCalculation TaxConfig SocialSecurityConfig]),
  "hr_manager" => Permission.where(action: %w[manage read], subject_class: "SalaryPackage")
                            .or(Permission.where(action: "read", subject_class: "SalaryCalculation")),
  "hr_officer" => Permission.where(action: %w[manage read], subject_class: "SalaryPackage")
                           .or(Permission.where(action: "read", subject_class: "SalaryCalculation")),
  "financial_manager" => Permission.where(action: "read", subject_class: "SalaryPackage")
                                   .or(Permission.where(action: %w[read calculate update submit approve], subject_class: "SalaryCalculation"))
                                   .or(Permission.where(action: "manage", subject_class: %w[TaxConfig SocialSecurityConfig])),
  "accountant" => Permission.where(action: "read", subject_class: "SalaryPackage")
                            .or(Permission.where(action: %w[read calculate update submit], subject_class: "SalaryCalculation")),
  "employee" => Permission.where(action: "read_own", subject_class: "SalaryPackage")
                          .or(Permission.where(action: "read_own", subject_class: "SalaryCalculation"))
}

# Assign permissions to roles
role_salary_permissions.each do |role_name, perms|
  role = Role.find_by!(name: role_name) # Fail if role doesn't exist

  perms.each do |permission|
    unless role.permissions.exists?(permission.id)
      role.permissions << permission
    end
  end
end

# Ensure ALL roles (except super_admin and admin) have basic salary permissions
puts "🔄 Adding basic salary permissions to all roles..."

basic_salary_permissions = Permission.where(action: "read_own", subject_class: "SalaryPackage", system_name: "people")
                                    .or(Permission.where(action: "read_own", subject_class: "SalaryCalculation", system_name: "people"))

Role.where.not(name: %w[super_admin admin]).each do |role|
  basic_salary_permissions.each do |permission|
    unless role.permissions.exists?(permission.id)
      role.permissions << permission
    end
  end
end

puts "✅ Salary permissions added and assigned to roles successfully!"
puts "✅ All roles now have access to their own salary information!"
