# Athar EMS Projects
projects = [
  { name: "TDH / SDC", description: "TDH / SDC", status: true },
  { name: "<PERSON><PERSON><PERSON>", description: "Safe spaces project for community support", status: true },
  { name: "<PERSON><PERSON><PERSON>", description: "Empowerment and capacity building project", status: true },
  { name: "Dare to Care", description: "Community care and support initiative", status: true },
  { name: "Good neighbors", description: "Neighborhood support and development program", status: true },
  { name: "War child", description: "Child protection and support program", status: true }
]

# Create projects
projects.each do |project_data|
  Project.find_or_create_by!(name: project_data[:name]) do |p|
    p.description = project_data[:description]
    p.status = project_data[:status]
  end
end

# Get project references for use in other seed files
$default_project = Project.find_by(name: "TDH / SDC")
$tdh_sdc_project = Project.find_by(name: "TDH / SDC")
$masahat_aminah_project = Project.find_by(name: "<PERSON><PERSON><PERSON>")
$tamkeen_project = Project.find_by(name: "<PERSON><PERSON><PERSON>")
$dare_to_care_project = Project.find_by(name: "Dare to Care")
$good_neighbors_project = Project.find_by(name: "Good neighbors")
$war_child_project = Project.find_by(name: "War child")

puts "✅ Projects seeded successfully!"
