# Define roles with their scope and hierarchy level
roles = [
  # Highest authority (level 50)
  { name: "super_admin", scope: :global_role, level: 50 },

  # Organization-wide authority (level 40)
  { name: "admin", scope: :global_role, level: 40 },

  # Department head level (level 30)
  { name: "financial_manager", scope: :global_role, level: 30 },
  { name: "hr_manager", scope: :global_role, level: 30 },
  { name: "procurement_manager", scope: :project_based, level: 30 },
  { name: "supervisor", scope: :project_based, level: 30 },

  # Mid-level management (level 20)
  { name: "project_manager", scope: :project_based, level: 20 },
  { name: "hr_officer", scope: :global_role, level: 20 },
  { name: "accountant", scope: :global_role, level: 20 },
  { name: "procurement_officer", scope: :project_based, level: 20 },
  { name: "psychologist", scope: :project_based, level: 20 },
  { name: "social_media_specialist", scope: :project_based, level: 20 },

  # Staff level (level 10) - Basic employee permissions
  { name: "case_manager", scope: :project_based, level: 10 },
  { name: "program_manager", scope: :project_based, level: 10 },
  { name: "coach", scope: :project_based, level: 10 },
  { name: "graphic_designer", scope: :project_based, level: 10 },
  { name: "community_mobilizer", scope: :project_based, level: 10 },
  { name: "club_facilitator", scope: :project_based, level: 10 },
  { name: "community_animator", scope: :project_based, level: 10 },
  { name: "housekeeping", scope: :project_based, level: 10 },
  { name: "fundraising_officer", scope: :project_based, level: 10 },
  { name: "employee", scope: :project_based, level: 10 },

  # Volunteer level (level 5)
  { name: "volunteer", scope: :project_based, level: 5 }
]

# Create roles if they don't exist
roles.each do |role_data|
  Role.find_or_create_by!(name: role_data[:name]) do |role|
    role.scope = role_data[:scope]
    role.level = role_data[:level]
  end
end

# Update existing roles with levels if they already exist
roles.each do |role_data|
  role = Role.find_by(name: role_data[:name])
  if role && role.level.nil?
    role.update!(level: role_data[:level])
  end
end

puts "✅ Roles seeded successfully!"
