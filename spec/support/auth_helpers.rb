module AuthHelpers
  # Generate a valid main token for testing
  def generate_main_token(user)
    payload = {
      sub: user.id,
      token_type: "main",
      email: user.email,
      name: user.name,
      exp: AtharAuth.main_token_expire_time.from_now.to_i
    }
    AtharAuth.encode_token(payload)
  end

  # Generate a valid session token for testing
  def generate_session_token(user, scope = 'core', project = nil)
    role = user.user_roles.by_project(project).first&.role
    payload = user.generate_session_token(scope, role, project, AtharAuth.session_token_expire_time.from_now.to_i)
    AtharAuth.encode_token(payload)
  end

  # Set the Authorization header with a valid session token
  def auth_headers(user, scope = 'core', project = nil)
    token = generate_session_token(user, scope, project)
    { 'Authorization' => "Bearer #{token}" }
  end

  # Helper to authenticate in controller specs with session token
  def sign_in(user, scope = 'core', project = nil)
    token = generate_session_token(user, scope, project)
    request.headers['Authorization'] = "Bearer #{token}"
  end

  # Helper to authenticate with main token in controller specs
  def sign_in_with_main_token(user)
    token = generate_main_token(user)
    request.headers['Authorization'] = "Bearer #{token}"
  end
end

RSpec.configure do |config|
  config.include AuthHelpers, type: :request
  config.include AuthHelpers, type: :controller
end
