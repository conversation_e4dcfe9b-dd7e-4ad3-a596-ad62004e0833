require 'rails_helper'

RSpec.describe Api::RolesController, type: :request do
  let(:user) { create(:user, global: true) }
  let(:role) { create(:role, name: 'admin', scope: :global_role) }
  let(:token) { user.generate_session_token('core', role, nil, 1.day.from_now.to_i) }
  let(:headers) { { 'Authorization' => "Bearer #{AtharAuth.encode_token(token)}" } }

  before do
    # Create permissions for the user
    permission = create(:permission, name: 'Read Roles', action: 'read', subject_class: 'Role', system_name: 'core')
    create_permission = create(:permission, name: 'Create Role', action: 'create', subject_class: 'Role', system_name: 'core')
    update_permission = create(:permission, name: 'Update Role', action: 'update', subject_class: 'Role', system_name: 'core')
    destroy_permission = create(:permission, name: 'Destroy Role', action: 'destroy', subject_class: 'Role', system_name: 'core')

    # Add permissions to the role
    role.permissions << permission
    role.permissions << create_permission
    role.permissions << update_permission
    role.permissions << destroy_permission

    # Assign the role to the user
    user.user_roles.create(role: role, project: nil)
  end

  describe 'GET /api/roles' do
    before do
      create_list(:role, 3)
    end

    it 'returns a list of roles' do
      get '/api/roles', headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      # There should be 4 roles (3 created in the before block + 1 admin role created in the main before block)
      expect(json['data'].size).to eq(4)
      expect(json['data'][0]['type']).to eq('role')
      expect(json['meta']).to have_key('pagination')
    end

    it 'filters roles by name' do
      create(:role, name: 'special_role')

      get '/api/roles', params: { filter: { name_cont: 'special' } }, headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data'].size).to eq(1)
      expect(json['data'][0]['attributes']['name']).to eq('special_role')
    end

    it 'paginates results' do
      create_list(:role, 10)

      get '/api/roles', params: { page: { number: 2, size: 5 } }, headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data'].size).to eq(5)
      expect(json['meta']['pagination']['page']).to eq(2)
    end
  end

  describe 'GET /api/roles/:id' do
    let(:role) { create(:role) }

    it 'returns a specific role' do
      get "/api/roles/#{role.id}", headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data']['id']).to eq(role.id.to_s)
      expect(json['data']['attributes']['name']).to eq(role.name)
    end

    it 'returns 404 for non-existent role' do
      get '/api/roles/999999', headers: headers

      expect(response).to have_http_status(:not_found)
    end
  end

  describe 'POST /api/roles' do
    let(:valid_attributes) do
      {
        data: {
          type: 'role',
          attributes: {
            name: 'new_role',
            scope: 'project_based'
          }
        }
      }
    end

    it 'creates a new role' do
      expect {
        post '/api/roles', params: valid_attributes, headers: headers
      }.to change(Role, :count).by(1)

      expect(response).to have_http_status(:created)
      json = JSON.parse(response.body)
      expect(json['data']['attributes']['name']).to eq('new_role')
      expect(json['data']['attributes']['scope']).to eq('project_based')
    end

    it 'returns validation errors for invalid data' do
      invalid_attributes = {
        data: {
          type: 'role',
          attributes: {
            name: '',
            scope: 'project_based'
          }
        }
      }

      post '/api/roles', params: invalid_attributes, headers: headers

      expect(response).to have_http_status(:unprocessable_entity)
      json = JSON.parse(response.body)
      expect(json['errors']).to be_present
    end
  end

  describe 'PATCH /api/roles/:id' do
    let(:role) { create(:role) }
    let(:update_attributes) do
      {
        data: {
          id: role.id.to_s,
          type: 'role',
          attributes: {
            name: 'updated_role_name',
            scope: 'global_role'
          }
        }
      }
    end

    it 'updates the role' do
      patch "/api/roles/#{role.id}", params: update_attributes, headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data']['attributes']['name']).to eq('updated_role_name')
      expect(json['data']['attributes']['scope']).to eq('global_role')
      expect(role.reload.name).to eq('updated_role_name')
      expect(role.reload.global_role?).to eq(true)
    end

    it 'returns validation errors for invalid data' do
      invalid_update = {
        data: {
          id: role.id.to_s,
          type: 'role',
          attributes: {
            name: ''
          }
        }
      }

      patch "/api/roles/#{role.id}", params: invalid_update, headers: headers

      expect(response).to have_http_status(:unprocessable_entity)
      json = JSON.parse(response.body)
      expect(json['errors']).to be_present
    end
  end

  describe 'DELETE /api/roles/:id' do
    let!(:role) { create(:role) }

    it 'deletes the role' do
      expect {
        delete "/api/roles/#{role.id}", headers: headers
      }.to change(Role, :count).by(-1)

      expect(response).to have_http_status(:no_content)
    end
  end
end
