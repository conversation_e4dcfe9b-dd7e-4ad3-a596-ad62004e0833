require 'rails_helper'

RSpec.describe Users::SessionsController, type: :controller do
  describe 'POST #create' do
    let(:user) { create(:user, password: 'password123') }
    let(:valid_params) { { user: { email: user.email, password: 'password123' } } }
    let(:invalid_params) { { user: { email: user.email, password: 'wrong_password' } } }

    context 'with valid credentials' do
      before { post :create, params: valid_params }

      it 'returns a successful response' do
        expect(response).to have_http_status(:created)
      end

      it 'returns a main token' do
        json = JSON.parse(response.body)
        expect(json['main_token']).to be_present
      end

      it 'returns user information' do
        json = JSON.parse(response.body)
        expect(json['user']['id']).to eq(user.id)
        expect(json['user']['email']).to eq(user.email)
      end
    end

    context 'with invalid credentials' do
      before { post :create, params: invalid_params }

      it 'returns unauthorized status' do
        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns an error message' do
        json = JSON.parse(response.body)
        expect(json['error']).to be_present
      end
    end
  end

  describe 'POST #session_token' do
    let(:user) { create(:user) }
    let(:main_token) { generate_main_token(user) }
    let(:valid_params) { { scope: 'core' } }
    let(:invalid_scope_params) { { scope: 'invalid_scope' } }

    context 'with valid main token and scope' do
      before do
        sign_in_with_main_token(user)
        post :session_token, params: valid_params
      end

      it 'returns a successful response' do
        expect(response).to have_http_status(:created)
      end

      it 'returns a session token' do
        json = JSON.parse(response.body)
        expect(json['session_token']).to be_present
      end

      it 'returns the correct scope' do
        json = JSON.parse(response.body)
        expect(json['scope']).to eq('core')
      end
    end

    context 'with invalid scope' do
      before do
        sign_in_with_main_token(user)
        post :session_token, params: invalid_scope_params
      end

      it 'returns unprocessable entity status' do
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns an error message' do
        json = JSON.parse(response.body)
        expect(json['error']).to be_present
      end
    end

    context 'without main token' do
      before { post :session_token, params: valid_params }

      it 'returns unauthorized status' do
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
