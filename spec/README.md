# Testing with RSpec

This project uses RSpec for testing. This document provides an overview of the testing setup and how to run tests.

## Running Tests

To run all tests:

```bash
bin/rspec
```

To run a specific test file:

```bash
bin/rspec spec/models/user_spec.rb
```

To run a specific test (by line number):

```bash
bin/rspec spec/models/user_spec.rb:10
```

## Test Structure

- `spec/models/` - Model specs
- `spec/requests/` - API request specs
- `spec/controllers/` - Controller specs
- `spec/factories/` - Factory Bot factories
- `spec/support/` - Support files and shared examples

## Test Database Setup

Before running tests, make sure your test database is set up:

```bash
bin/rails db:test:prepare
```

## Code Coverage

SimpleCov is configured to generate code coverage reports. After running tests, you can view the coverage report by opening `coverage/index.html` in your browser.

Note: The `coverage` directory is excluded from version control via `.gitignore`. This is standard practice as coverage reports are specific to your local environment and can be quite large.

## Factories

We use Factory Bot for test data generation. Factories are defined in the `spec/factories/` directory.

Example usage:

```ruby
# Create a user
user = create(:user)

# Create a user with traits
admin = create(:user, :global_user)

# Create a user with associations
user_with_role = create(:user_with_role, role_name: 'employee', project: project)
```

## Authentication in Tests

For controller and request specs that require authentication, use the `auth_headers` helper:

```ruby
# In a request spec
get '/api/users/me', headers: auth_headers(user)

# In a controller spec
sign_in(user)
```

## JSON Response Helpers

For API tests, use the JSON helpers to parse responses:

```ruby
# Parse JSON response
json_response

# Parse JSON response with symbolized keys
json_response_symbolized
```
