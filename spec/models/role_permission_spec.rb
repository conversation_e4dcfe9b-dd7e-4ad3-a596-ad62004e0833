require 'rails_helper'

RSpec.describe RolePermission, type: :model do
  describe 'associations' do
    it { should belong_to(:role) }
    it { should belong_to(:permission) }
  end
  
  describe 'creating role permissions' do
    let(:role) { create(:role) }
    let(:permission) { create(:permission) }
    
    it 'can be created with valid attributes' do
      role_permission = RolePermission.new(role: role, permission: permission)
      expect(role_permission).to be_valid
    end
    
    it 'cannot be created without a role' do
      role_permission = RolePermission.new(permission: permission)
      expect(role_permission).not_to be_valid
    end
    
    it 'cannot be created without a permission' do
      role_permission = RolePermission.new(role: role)
      expect(role_permission).not_to be_valid
    end
  end
end
