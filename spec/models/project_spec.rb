require 'rails_helper'

RSpec.describe Project, type: :model do
  describe 'associations' do
    it { should have_many(:user_roles).dependent(:destroy) }
    it { should have_many(:users).through(:user_roles) }
    it { should have_many(:roles).through(:user_roles) }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_uniqueness_of(:name) }
  end

  describe 'status attribute' do
    it 'has active and inactive scopes' do
      active_project = create(:project, status: true)
      inactive_project = create(:project, status: false)

      expect(Project.active).to include(active_project)
      expect(Project.active).not_to include(inactive_project)
      expect(Project.inactive).to include(inactive_project)
      expect(Project.inactive).not_to include(active_project)
    end
  end

  describe 'callbacks' do
    it 'sets default status to active' do
      project = Project.new
      expect(project.status).to eq('active')
    end
  end

  describe 'scopes' do
    let!(:active_project) { create(:project, status: :active) }
    let!(:inactive_project) { create(:project, status: :inactive) }
    let(:user) { create(:user) }
    let(:role) { create(:role, name: 'employee') }
    let(:permission) { create(:permission, name: 'read_project') }

    before do
      role.permissions << permission
      create(:user_role, user: user, role: role, project: active_project)
    end

    describe '.active' do
      it 'returns only active projects' do
        expect(Project.active).to include(active_project)
        expect(Project.active).not_to include(inactive_project)
      end
    end

    describe '.inactive' do
      it 'returns only inactive projects' do
        expect(Project.inactive).to include(inactive_project)
        expect(Project.inactive).not_to include(active_project)
      end
    end

    describe '.with_user' do
      it 'returns projects associated with the user' do
        expect(Project.with_user(user)).to include(active_project)
        expect(Project.with_user(user)).not_to include(inactive_project)
      end
    end

    describe '.with_role' do
      it 'returns projects with the specified role' do
        expect(Project.with_role('employee')).to include(active_project)
      end
    end

    describe '.with_permission' do
      it 'returns projects with the specified permission' do
        expect(Project.with_permission('read_project')).to include(active_project)
      end
    end

    describe '.with_user_having_role' do
      it 'returns projects where the user has the specified role' do
        expect(Project.with_user_having_role(user, 'employee')).to include(active_project)
      end
    end

    describe '.with_user_having_permission' do
      it 'returns projects where the user has the specified permission' do
        expect(Project.with_user_having_permission(user, 'read_project')).to include(active_project)
      end
    end
  end

  describe '#users_with_role' do
    let(:project) { create(:project) }
    let(:user) { create(:user) }
    let(:role) { create(:role, name: 'employee') }

    before do
      create(:user_role, user: user, role: role, project: project)
    end

    it 'returns users with the specified role in the project' do
      expect(project.users_with_role('employee')).to include(user)
    end

    it 'returns an empty relation if the role does not exist' do
      expect(project.users_with_role('nonexistent')).to be_empty
    end
  end

  describe '#user_has_role?' do
    let(:project) { create(:project) }
    let(:user) { create(:user) }
    let(:role) { create(:role, name: 'employee') }

    context 'when user has the role in the project' do
      before do
        create(:user_role, user: user, role: role, project: project)
      end

      it 'returns true' do
        expect(project.user_has_role?(user, 'employee')).to be true
      end
    end

    context 'when user does not have the role in the project' do
      it 'returns false' do
        expect(project.user_has_role?(user, 'employee')).to be false
      end
    end

    context 'when user is nil' do
      it 'returns false' do
        expect(project.user_has_role?(nil, 'employee')).to be false
      end
    end
  end

  describe '#add_user' do
    let(:project) { create(:project) }
    let(:user) { create(:user) }
    let!(:employee_role) { create(:role, name: 'employee') }

    it 'adds the user to the project with the specified role' do
      expect {
        project.add_user(user, 'employee')
      }.to change { user.user_roles.count }.by(1)

      expect(project.user_has_role?(user, 'employee')).to be true
    end

    it 'does nothing if user is nil' do
      expect {
        project.add_user(nil, 'employee')
      }.not_to change { UserRole.count }
    end
  end

  describe '#remove_user' do
    let(:project) { create(:project) }
    let(:user) { create(:user) }
    let(:role) { create(:role, name: 'employee') }

    before do
      create(:user_role, user: user, role: role, project: project)
    end

    it 'removes all roles the user has in the project' do
      expect {
        project.remove_user(user)
      }.to change { user.user_roles.count }.by(-1)

      expect(project.user_has_role?(user, 'employee')).to be false
    end

    it 'does nothing if user is nil' do
      expect {
        project.remove_user(nil)
      }.not_to change { UserRole.count }
    end
  end

  describe '#assign_role_to_user' do
    let(:project) { create(:project) }
    let(:user) { create(:user) }
    let!(:employee_role) { create(:role, name: 'employee') }

    it 'assigns the specified role to the user in the project' do
      expect {
        project.assign_role_to_user(user, 'employee')
      }.to change { user.user_roles.count }.by(1)

      expect(project.user_has_role?(user, 'employee')).to be true
    end

    it 'does nothing if user is nil' do
      expect {
        project.assign_role_to_user(nil, 'employee')
      }.not_to change { UserRole.count }
    end
  end

  describe '#members' do
    let(:project) { create(:project) }
    let(:user1) { create(:user) }
    let(:user2) { create(:user) }
    let(:role) { create(:role, name: 'employee') }

    before do
      create(:user_role, user: user1, role: role, project: project)
      create(:user_role, user: user2, role: role, project: project)
    end

    it 'returns all users in the project' do
      expect(project.members).to include(user1, user2)
    end
  end

  describe '#member_count' do
    let(:project) { create(:project) }
    let(:user1) { create(:user) }
    let(:user2) { create(:user) }
    let(:role) { create(:role, name: 'employee') }

    before do
      create(:user_role, user: user1, role: role, project: project)
      create(:user_role, user: user2, role: role, project: project)
    end

    it 'returns the count of users in the project' do
      expect(project.member_count).to eq(2)
    end
  end

  describe '#to_rpc_response' do
    let(:project) { create(:project, name: 'Test Project') }

    it 'returns a properly formatted RPC response' do
      response = project.to_rpc_response

      expect(response).to be_a(Core::ProjectResponse)
      expect(response.id).to eq(project.id.to_s)
      expect(response.name).to eq('Test Project')
    end
  end
end
