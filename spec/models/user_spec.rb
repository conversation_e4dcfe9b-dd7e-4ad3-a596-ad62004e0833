require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'associations' do
    it { should have_one_attached(:avatar) }
    it { should have_many(:user_roles).dependent(:destroy) }
    it { should have_many(:roles).through(:user_roles) }
    it { should have_many(:projects).through(:user_roles) }
    it { should have_many(:permissions).through(:roles) }
  end

  describe 'validations' do
    it { should validate_presence_of(:email) }
    it { should validate_uniqueness_of(:email).case_insensitive }
    it { should allow_value('<EMAIL>').for(:email) }
    it { should_not allow_value('invalid_email').for(:email) }

    context 'when creating a new user' do
      it { should validate_length_of(:password).is_at_least(6) }
    end
  end

  describe 'enums' do
    it 'defines status enum' do
      expect(User.statuses).to eq({ 'inactive' => false, 'active' => true })
    end

    it 'defines global enum' do
      expect(User.globals).to eq({ 'project_based' => false, 'global_user' => true })
    end
  end

  describe 'callbacks' do
    it 'sets default status to active' do
      user = User.new
      expect(user.status).to eq('active')
    end
  end

  describe '#has_role?' do
    let(:user) { create(:user) }
    let(:project) { create(:project) }
    let(:role) { create(:role, name: 'employee') }

    context 'when user has the role in the project' do
      before do
        create(:user_role, user: user, role: role, project: project)
      end

      it 'returns true' do
        expect(user.has_role?('employee', project)).to be true
      end
    end

    context 'when user does not have the role in the project' do
      it 'returns false' do
        expect(user.has_role?('employee', project)).to be false
      end
    end

    context 'when user has a global role' do
      let(:global_role) { create(:role, name: 'admin', scope: :global_role) }

      before do
        user.update(global: true)
        create(:user_role, user: user, role: global_role, project: nil)
      end

      it 'returns true regardless of project' do
        expect(user.has_role?('admin', nil)).to be true
        expect(user.has_role?('admin', project)).to be true
      end
    end
  end

  describe '#add_role' do
    let(:user) { create(:user) }
    let(:project) { create(:project) }
    let!(:role) { create(:role, name: 'employee') }

    it 'adds a role to the user in the specified project' do
      expect {
        user.add_role('employee', project)
      }.to change { user.user_roles.count }.by(1)

      expect(user.has_role?('employee', project)).to be true
    end

    context 'with a global role' do
      let!(:global_role) { create(:role, name: 'admin', scope: :global_role) }

      it 'adds a global role to the user' do
        expect {
          user.add_role('admin', nil)
        }.to change { user.user_roles.count }.by(1)
                                             .and change { user.global_user? }.from(false).to(true)

        expect(user.has_role?('admin', nil)).to be true
      end
    end
  end
end
