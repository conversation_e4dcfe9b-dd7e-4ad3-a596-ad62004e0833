require 'rails_helper'

RSpec.describe UserRole, type: :model do
  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:role) }
    it { should belong_to(:project).optional }
  end

  describe 'validations' do
    let(:user) { create(:user) }
    let(:role) { create(:role) }
    let(:project) { create(:project) }

    context 'with project' do
      it 'validates uniqueness of user_id scoped to project_id' do
        create(:user_role, user: user, role: role, project: project)
        duplicate = build(:user_role, user: user, role: create(:role), project: project)
        expect(duplicate).not_to be_valid
        expect(duplicate.errors[:user_id]).to include('can have only one role per project')
      end
    end

    context 'without project (global role)' do
      let(:global_role) { create(:role, scope: :global_role) }
      let(:global_user) { create(:user, global: :global_user) }

      it 'validates uniqueness of user_id scoped to role_id' do
        create(:user_role, user: global_user, role: global_role, project: nil)
        duplicate = build(:user_role, user: global_user, role: global_role, project: nil)
        expect(duplicate).not_to be_valid
        expect(duplicate.errors[:user_id]).to include('already has this global role')
      end
    end

    it 'validates role-project consistency' do
      global_role = create(:role, scope: :global_role)
      user_role = build(:user_role, user: user, role: global_role, project: project)
      expect(user_role).not_to be_valid
      expect(user_role.errors[:base]).to include(/Global roles cannot be assigned to projects/)
    end

    it 'validates role-user consistency' do
      global_role = create(:role, scope: :global_role)
      user.update(global: false)
      user_role = build(:user_role, user: user, role: global_role, project: nil)
      expect(user_role).not_to be_valid
      expect(user_role.errors[:base]).to include(/Global roles can only be assigned to global users/)
    end

    it 'validates only one default per user' do
      create(:user_role, user: user, role: role, project: project, is_default: true)
      another_role = build(:user_role, user: user, role: create(:role), project: create(:project), is_default: true)
      expect(another_role).not_to be_valid
      expect(another_role.errors[:is_default]).to include('User can only have one default project role')
    end
  end

  describe 'scopes' do
    let(:project) { create(:project) }
    let!(:user_role) { create(:user_role, project: project) }
    let!(:other_user_role) { create(:user_role, project: create(:project)) }

    describe '.by_project' do
      it 'returns user roles for the specified project' do
        expect(UserRole.by_project(project)).to include(user_role)
        expect(UserRole.by_project(project)).not_to include(other_user_role)
      end
    end
  end

  describe '#set_as_default!' do
    let(:user) { create(:user) }
    let(:role) { create(:role) }
    let(:project1) { create(:project) }
    let(:project2) { create(:project) }
    let!(:user_role1) { create(:user_role, user: user, role: role, project: project1, is_default: true) }
    let!(:user_role2) { create(:user_role, user: user, role: role, project: project2, is_default: false) }

    it 'sets the user role as default and unsets any existing default' do
      expect(user_role1.is_default).to be true
      expect(user_role2.is_default).to be false

      user_role2.set_as_default!

      user_role1.reload
      user_role2.reload

      expect(user_role1.is_default).to be false
      expect(user_role2.is_default).to be true
    end
  end

  describe '#to_rpc_response' do
    let(:test_user) { create(:user) }
    let(:test_role) { create(:role) }
    let(:test_project) { create(:project) }
    let(:user_role) { create(:user_role, user: test_user, role: test_role, project: test_project, is_default: true) }

    it 'returns a properly formatted RPC response' do
      response = user_role.to_rpc_response

      expect(response).to be_a(Core::UserRoleResponse)
      expect(response.id).to eq(user_role.id.to_s)
      expect(response.role).to be_a(Core::RoleResponse)
      expect(response.project).to be_a(Core::ProjectResponse) if user_role.project
      expect(response.is_default).to be true
    end
  end
end
