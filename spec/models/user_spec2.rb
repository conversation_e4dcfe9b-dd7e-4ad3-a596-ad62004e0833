require 'rails_helper'

RSpec.describe User, type: :model do
  describe '#user_roles=' do
    let(:user) { create(:user) }
    let(:role1) { create(:role, name: 'admin', global_role: true) }
    let(:role2) { create(:role, name: 'project_manager', global_role: false) }
    let(:project) { create(:project) }

    context 'with valid role data' do
      let(:roles_data) do
        [
          { role_id: role1.id, is_default: true },
          { role_id: role2.id, project_id: project.id }
        ]
      end

      it 'assigns multiple roles to a user' do
        user.user_roles = roles_data
        user.reload

        # Check that roles were assigned
        expect(user.user_roles.count).to eq(2)
        expect(user.roles).to include(role1)
        expect(user.roles).to include(role2)

        # Check that default role was set
        expect(user.user_roles.find_by(role_id: role1.id).is_default).to be true

        # Check that global flag was updated
        expect(user.global).to be true
      end

      it 'replaces existing roles' do
        # First add some roles
        existing_role = create(:role, name: 'employee')
        user.user_roles.create(role: existing_role)
        expect(user.user_roles.count).to eq(1)

        # Then set new roles
        user.user_roles = roles_data
        user.reload

        # Check that old roles were removed
        expect(user.user_roles.count).to eq(2)
        expect(user.roles).not_to include(existing_role)
      end

      it 'sets first role as default if none marked as default' do
        roles_data = [
          { role_id: role1.id, is_default: false },
          { role_id: role2.id, project_id: project.id, is_default: false }
        ]

        user.user_roles = roles_data
        user.reload

        # Check that first role was set as default
        expect(user.user_roles.first.is_default).to be true
      end
    end

    context 'with invalid role data' do
      it 'skips roles with missing role_id' do
        roles_data = [
          { project_id: project.id, is_default: true },
          { role_id: role2.id, project_id: project.id }
        ]

        user.user_roles = roles_data
        user.reload

        expect(user.user_roles.count).to eq(1)
        expect(user.roles).to include(role2)
      end

      it 'does nothing if roles_data is empty' do
        user.user_roles = []
        expect(user.user_roles.count).to eq(0)

        user.user_roles = nil
        expect(user.user_roles.count).to eq(0)
      end
    end
  end
end
