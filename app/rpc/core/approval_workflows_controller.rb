module Core
  class ApprovalWorkflowsController < AtharRpc::Grpc::BaseController
    bind ::Core::ApprovalWorkflows::Service

    # Get approval workflow for a specific action/subject
    def get_workflow
      process_request do
        req = request.message

        # Validate required parameters
        validate_required_params!(req, :action, :subject_class, :system_name)

        # Find the workflow
        workflow = ApprovalWorkflow.find_by(
          action: req.action,
          subject_class: req.subject_class,
          system_name: req.system_name
        )

        unless workflow
          fail!(:not_found, :workflow_not_found, "No workflow found for the specified parameters")
        end

        workflow.to_rpc_response
      end
    end

    # Generate approval sequence for a specific request
    def generate_sequence
      process_request do
        req = request.message

        # Validate required parameters
        validate_required_params!(req, :action, :subject_class, :system_name, :requestor_id)

        # Find the workflow
        workflow = ApprovalWorkflow.active.find_by(
          action: req.action,
          subject_class: req.subject_class,
          system_name: req.system_name
        )

        unless workflow
          fail!(:not_found, :workflow_not_found, "No workflow found for the specified parameters")
        end

        # Find the requestor
        requestor = find_record(User, req.requestor_id)

        # Find the project if provided
        project = nil
        if req.project_id.present?
          project = find_record(Project, req.project_id)
        end

        # Extract context from request
        context = extract_context(req)

        # Check for required context keys
        validate_context_keys(workflow, context)

        # Generate the sequence
        sequence = workflow.generate_sequence(requestor.id, project&.id, context)

        # Check if the workflow applies to this context
        unless sequence
          fail!(:failed_precondition, :workflow_not_applicable, "Workflow does not apply to the given context")
        end

        # Build the response
        build_sequence_response(sequence)
      end
    end

    # Validate if a user can approve a specific step
    def validate_approver
      process_request do
        req = request.message

        # Validate required parameters
        validate_required_params!(req, :user_id, :approver_ids)

        # Find the user
        user = find_record(User, req.user_id)

        # Check if user is in the approver list
        is_approver = req.approver_ids.include?(user.id.to_s)

        # Return the result
        ::Core::ValidateApproverResponse.new(
          can_approve: is_approver
        )
      end
    end

    # List all workflows for a system
    def list_workflows
      process_request do
        req = request.message

        # Validate required parameters
        validate_required_params!(req, :system_name)

        # Create the base query
        base_query = ApprovalWorkflow.where(system_name: req.system_name)

        # Execute the query and return results
        execute_query(base_query, req,
                      response_class: ::Core::ApprovalWorkflowListResponse,
                      transformer: :to_rpc_response
        )
      end
    end

    private

    # Extract context from request
    def extract_context(req)
      context = {}
      if req.respond_to?(:context) && req.context.present?
        req.context.each do |key, value|
          context[key.to_sym] = value
        end
      end
      context
    end

    # Validate context keys against workflow requirements
    def validate_context_keys(workflow, context)
      if workflow.required_context_keys.present?
        missing_keys = workflow.required_context_keys - context.keys.map(&:to_s)
        if missing_keys.any?
          fail!(:invalid_argument, :missing_context_keys, "Missing required context keys: #{missing_keys.join(', ')}")
        end
      end
    end

    # Build sequence response from sequence data
    def build_sequence_response(sequence)
      response = ::Core::ApprovalSequenceResponse.new(
        workflow_id: sequence[:workflow_id].to_s,
        workflow_name: sequence[:workflow_name]
      )

      # Add auto_approved flag and reason if present
      if sequence.key?(:auto_approved)
        response.auto_approved = sequence[:auto_approved]
        response.auto_approval_reason = sequence[:auto_approval_reason] if sequence.key?(:auto_approval_reason)
      end

      # Add steps
      if sequence[:steps].present?
        sequence[:steps].each do |step|
          step_seq = ::Core::ApprovalStepSequence.new(
            step_id: step[:step_id].to_s,
            name: step[:name],
            sequence: step[:sequence],
            approval_type: step[:approval_type],
            selection_explanation: step[:selection_explanation].to_s
          )

          # Ensure approver_ids is an array of strings
          approver_ids = Array(step[:approver_ids] || [])
          approver_ids.each do |id|
            step_seq.approver_ids.push(id.to_s)
          end

          response.steps.push(step_seq)
        end
      end

      response
    end
  end
end
