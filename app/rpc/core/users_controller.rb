module Core
  class UsersController < AtharRpc::Grpc::BaseController
    bind ::Core::Users::Service

    # GET USER
    def get_user
      get_resource(User, request.message,
                   transformer: :to_rpc_response,
                   includes: [ :avatar_attachment, user_roles: [ :role, :project ] ]
      )
    end

    # LIST USERS
    def list_users
      list_resources(User, request.message,
                     response_class: ::Core::UserListResponse,
                     transformer: :to_rpc_response,
                     includes: [ :avatar_attachment, user_roles: [ :role, :project ] ]
      )
    end

    # UPDATE USER
    def update_user
      update_resource(User, request.message,
                      param_names: [ :name, :email, :status, :password, :avatar_attributes, :user_roles_list ],
                      param_extractor: method(:extract_user_params),
                      transformer: :to_rpc_response
      )
    end

    # CREATE USER
    def create_user
      create_resource(User, request.message,
                      param_names: [ :name, :email, :status, :password, :avatar_attributes, :user_roles_list ],
                      required_params: [ :name, :email, :password ],
                      param_extractor: method(:extract_user_params),
                      transformer: :to_rpc_response
      )
    end

    private

    # Extract user parameters from request
    def extract_user_params(request_payload)
      params = extract_params(request_payload,
                              :name, :email, :status, :password, :avatar_attributes, :user_roles_list
      )

      # Any additional parameter processing can be done here

      params
    end
  end
end
