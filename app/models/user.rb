class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable, :recoverable, :validatable

  include Athar::Commons::Models::Concerns::Ransackable

  has_one_attached :avatar
  has_many :user_roles, dependent: :destroy
  has_many :roles, through: :user_roles
  has_many :projects, through: :user_roles
  has_many :permissions, through: :roles

  accepts_nested_attributes_for :user_roles, allow_destroy: true, reject_if: :all_blank

  # Scopes
  scope :in_project, ->(project) { joins(:user_roles).where(user_roles: { project_id: project.id }).distinct }
  scope :with_role, ->(role_name) { joins(user_roles: :role).where(roles: { name: role_name }).distinct }
  scope :with_role_in_project, ->(role_name, project) {
    includes(user_roles: :role)
      .where(roles: { name: role_name }, user_roles: { project_id: project.id })
      .distinct
  }
  scope :with_permission, ->(permission_name) {
    includes(roles: :permissions).where(permissions: { name: permission_name }).distinct
  }
  scope :with_permission_in_project, ->(permission_name, project) {
    includes(user_roles: { role: :permissions })
      .where(permissions: { name: permission_name }, user_roles: { project_id: project.id })
      .distinct
  }

  enum :status, { inactive: false, active: true }
  enum :global, { project_based: false, global_user: true }

  # Validations for user type
  validate :validate_role_consistency, if: :persisted?

  after_initialize :set_default_status, if: :new_record?

  validates :email, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :password, length: { minimum: 6 }, if: -> { new_record? || !password.nil? }

  def generate_session_token(scope, role, project, exp)
    # Validate scope parameter
    valid_scopes = %w[core people procure case_manager]
    unless valid_scopes.include?(scope)
      raise ArgumentError, " Invalid scope. Must be one of: #{valid_scopes.join(', ')}"
    end

    if !global_user? && project&.id&.blank?
      raise ArgumentError, "Project ID is required for non-global users"
    end
    {
      sub: id,
      token_type: "session",
      scope: scope,
      user_type: user_type,
      project_id: project&.id,
      project_name: project&.name,
      role: role,
      permissions: role&.permissions&.by_system(scope)&.map(&:to_s) || [],
      iat: Time.now.to_i,
      exp: exp
    }
  end

  def user_type
    global_user? ? "global" : "project_based"
  end

  def default_role
    user_roles.find_by(is_default: true)&.role
  end

  def has_role?(role_name, project = nil)
    if project.nil?
      # For global roles, check if the user has the role with no project
      user_roles.joins(:role).exists?(roles: { name: role_name, scope: :global_role }, project: nil)
    else
      # For global roles, they apply to all projects
      global_role = user_roles.joins(:role).exists?(roles: { name: role_name, scope: :global_role }, project: nil)
      return true if global_role

      # For project roles, check if the user has the role for this project
      user_roles.joins(:role).exists?(roles: { name: role_name }, project: project)
    end
  end

  def add_role(role_name, project)
    role = Role.find_by(name: role_name)
    return unless role

    # For global roles, project can be nil
    if project.nil? && role.global_role?
      # Create a user_role with nil project for global roles
      user_roles.create(role: role, project: nil)
      update(global: true) unless global_user?
      return
    end

    # For project-specific roles, project is required
    return unless project

    # First remove any existing role for this project
    existing_role = user_roles.where(project: project).first
    user_roles.destroy(existing_role) if existing_role

    # Then add the new role
    user_roles.create(role: role, project: project)
  end

  def default_project
    user_roles.default.first&.project
  end

  # Avatar methods
  def avatar_attributes
    return nil unless avatar.attached?

    {
      url: avatar.cdn_url
    }
  end

  # Setter method for avatar attributes from RPC requests
  def avatar_attributes=(attrs)
    return unless attrs.present?

    # Extract attributes
    image_data = attrs.is_a?(Hash) ? attrs[:image] : attrs.image
    filename = attrs.is_a?(Hash) ? (attrs[:filename] || 'avatar.jpg') : (attrs.filename.presence || 'avatar.jpg')
    content_type = attrs.is_a?(Hash) ? attrs[:content_type] : attrs.content_type

    return unless image_data.present?

    # Build the attachment without saving immediately
    avatar.attach(
      io: StringIO.new(image_data),
      filename: filename,
      content_type: content_type
    )
  end

  # Getter method for user_roles as a list of hashes
  def user_roles_list
    user_roles.map(&:to_rpc_response)
  end

  # Setter method for user_roles from RPC requests
  def user_roles_list=(roles_data)
    self.user_roles_attributes = (roles_data || []).map do |role_data|
      role_data.dup.except(:role, :project)
    end
  end

  def to_rpc_response
    ::Core::UserResponse.new(
      id: id.to_s,
      name: name,
      email: email,
      status: status.to_s,
      avatar_attributes: avatar_attributes,
      user_roles_list: user_roles_list
    )
  end

  # Dynamically define role checking methods (e.g., user.super_admin?(project))
  Role.pluck(:name).each do |role_name|
    define_method("#{role_name}?") do |project|
      has_role?(role_name, project)
    end
  end

  private

  def set_default_status
    self.status ||= :active
  end

  def validate_role_consistency
    # Get user_roles that are not marked for destruction
    active_user_roles = user_roles.reject(&:marked_for_destruction?)

    # Check if any active user_roles have roles with the wrong global setting
    has_project_roles = active_user_roles.any? { |ur| ur.role&.project_based? }
    has_global_roles = active_user_roles.any? { |ur| ur.role&.global_role? }

    if global_user? && has_project_roles
      errors.add(:base, "Global users cannot have project-specific roles")
    elsif project_based? && has_global_roles
      errors.add(:base, "Project users cannot have global roles")
    end
  end
end
