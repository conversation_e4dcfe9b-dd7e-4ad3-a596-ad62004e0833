class ApprovalWorkflow < ApplicationRecord
  has_many :approval_steps, dependent: :destroy

  # Validations
  validates :name, presence: true
  validates :action, presence: true
  validates :subject_class, presence: true
  validates :system_name, presence: true
  validates :empty_steps_behavior, inclusion: { in: %w[auto_approve error] }, allow_nil: true

  # Scopes
  scope :active, -> { where(active: true) }

  # Find a workflow by its key attributes
  def self.find_by_key(action, subject_class, system_name)
    find_by(
      action: action,
      subject_class: subject_class,
      system_name: system_name,
      active: true
    )
  end

  # Check if this workflow should be applied based on the context
  def should_apply?(context)
    return true if condition.blank?

    # Validate required context keys
    if required_context_keys.present?
      missing_keys = required_context_keys - context.keys.map(&:to_s)
      if missing_keys.any?
        Rails.logger.warn "Missing required context keys for workflow '#{name}': #{missing_keys.join(', ')}"
        return false
      end
    end

    # Validate context values
    begin
      # Check for numeric values where needed
      if condition.include?(".to_f") || condition.include?(".to_i")
        # Extract keys used in numeric operations
        numeric_keys = condition.scan(/([\w_]+)\.to_(f|i)/).map(&:first).uniq
        numeric_keys.each do |key|
          value = context[key.to_sym] || context[key]
          next unless value.present?

          # Try to convert to number
          begin
            Float(value)
          rescue ArgumentError
            Rails.logger.warn "Invalid numeric value for context key '#{key}' in workflow '#{name}': #{value}"
            return false
          end
        end
      end

      # Create a safe evaluation context
      evaluation_context = ApprovalStep::SafeEvaluationContext.new(context)

      # Evaluate the condition in the context
      evaluation_context.evaluate(condition)
    rescue => e
      Rails.logger.error "Error evaluating workflow condition '#{condition}': #{e.message}"
      Rails.logger.error e.backtrace.join("\n") if e.backtrace
      true # Apply the workflow by default if there's an error
    end
  end

  # Generate an approval sequence for a specific request
  def generate_sequence(requestor_id, project_id = nil, context = {})
    # Check if this workflow should be applied
    return nil unless should_apply?(context)

    # Get all steps for the workflow
    steps = approval_steps.order(sequence: :asc)

    # Initialize a hash to store information about previous steps
    previous_steps = {}

    # Generate sequence with dynamic approvers
    sequence = {
      workflow_id: id,
      workflow_name: name,
      steps: []
    }

    # Process each step in sequence order
    steps.each do |step|
      # Prepare context for this step, including previous steps information
      step_context = context.dup
      step_context[:previous_steps] = previous_steps if previous_steps.present?

      # Check if the step should be included based on the updated context
      unless step.should_include?(step_context)
        Rails.logger.info "Skipping step #{step.name} (#{step.id}) because condition evaluated to false"
        next
      end

      # Resolve approvers dynamically with explanation
      if step.dynamic_approver_method.present?
        # Validate that the method exists
        unless ApprovalEngine.respond_to?(step.dynamic_approver_method)
          error_message = "Dynamic approver method '#{step.dynamic_approver_method}' does not exist in ApprovalEngine"
          Rails.logger.error error_message
          step.update(selection_explanation: error_message)
          approver_ids = []
        else
          begin
            result = ApprovalEngine.send(step.dynamic_approver_method, requestor_id, project_id, step_context)

            if result.is_a?(Hash) && result[:approvers] && result[:explanation]
              approver_ids = result[:approvers]
              explanation = result[:explanation]
            else
              # For backward compatibility with methods that don't return explanations
              approver_ids = result
              explanation = "Approvers selected using #{step.dynamic_approver_method}"
            end

            # Store the explanation
            step.update(selection_explanation: explanation)
          rescue => e
            error_message = "Error calling dynamic approver method '#{step.dynamic_approver_method}': #{e.message}"
            Rails.logger.error error_message
            Rails.logger.error e.backtrace.join("\n") if e.backtrace
            step.update(selection_explanation: error_message)
            approver_ids = []
          end
        end
      else
        approver_ids = step.static_approver_ids || []
        step.update(selection_explanation: "Using static approver IDs: #{approver_ids.join(', ')}")
      end

      # Store information about this step for use in subsequent steps
      previous_steps[step.sequence] = {
        approvers: approver_ids,
        explanation: step.selection_explanation
      }

      # Skip steps with no approvers if configured to do so
      next if approver_ids.empty? && step.skip_if_no_approvers

      # Add the step to the sequence
      sequence[:steps] << {
        step_id: step.id,
        name: step.name,
        sequence: step.sequence,
        approval_type: step.approval_type,
        approver_ids: approver_ids,
        selection_explanation: step.selection_explanation
      }
    end

    # Handle empty steps based on configuration
    if sequence[:steps].empty?
      behavior = empty_steps_behavior || 'auto_approve'

      if behavior == 'error'
        error_message = "No applicable steps found for workflow: #{name}"
        Rails.logger.error error_message
        raise error_message
      end

      # For auto_approve, return an empty sequence with explanation
      auto_approval_reason = if condition.present? && context.present?
                               "Auto-approved because no steps apply with the given context: #{context.inspect}"
      else
                               "Auto-approved because no steps are defined or applicable"
      end

      Rails.logger.info "Workflow '#{name}' auto-approved: #{auto_approval_reason}"

      return {
        workflow_id: id,
        workflow_name: name,
        steps: [],
        auto_approved: true,
        auto_approval_reason: auto_approval_reason
      }
    end

    sequence
  end
end
