class Role < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  has_many :user_roles, dependent: :destroy
  has_many :users, through: :user_roles
  has_many :projects, through: :user_roles
  has_many :role_permissions, dependent: :destroy
  has_many :permissions, through: :role_permissions

  validates :name, presence: true, uniqueness: true

  enum :scope, { project_based: false, global_role: true }

  after_save :refresh_user_role_methods
  after_destroy :refresh_user_role_methods

  def to_rpc_response
    ::Core::RoleResponse.new(
      id: id.to_s,
      name: name,
      global: global_role?,
      level: level
    )
  end

  def refresh_user_role_methods
    role_names = Role.pluck(:name)
    User.class_eval do
      role_names.each do |role_name|
        method_name = "#{role_name}?"
        next if method_defined?(method_name)
        define_method(method_name) do |project = nil|
          has_role?(role_name, project)
        end
      end
    end
  end
end
