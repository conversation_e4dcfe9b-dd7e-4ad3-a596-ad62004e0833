class UsersController < ApplicationController
  before_action :authenticate_session!
  before_action :set_permissions, only: [ :permissions ]

  api :GET, "/users/me", "Returns the currently authenticated user"
  header "Authorization", "Scoped session token as Bearer token", required: true
  description <<-HTML
    Returns the authenticated user's basic information in JSON:API format.

    Requires a valid <b>scoped session token</b> issued via <code>/auth/session_token</code>.
    Token must be scoped to one of the subsystems: <code>core</code>, <code>people</code>, <code>case_manager</code>, or <code>procure</code>.

    <b>Permission required:</b> <code>:read, :user</code>
  HTML
  returns code: 200, desc: "Authenticated user information in JSON:API format" do
    property :data, Hash, desc: "JSON:API data object" do
      property :id, String, desc: "User ID"
      property :type, String, desc: "Resource type (user)"
      property :attributes, Hash, desc: "User attributes" do
        property :email, String, desc: "User email"
        property :name, String, desc: "User's full name"
        property :status, String, desc: "User status (active/inactive)"
        property :avatar, String, desc: "CDN URL of the profile image (if available)"
        property :user_roles, Array, desc: "Array of user roles with role and project details"
      end
    end
    property :meta, Hash, desc: "Metadata" do
      property :systems, Array, desc: "Available systems" do
        property :name, String, desc: "System identifier"
        property :alias, String, desc: "Short alias for building URLs"
        property :display_name, String, desc: "System display name"
        property :description, String, desc: "System description"
      end
    end
  end
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"
  example <<-JSON
  {
    "data": {
      "id": "5",
      "type": "user",
      "attributes": {
        "email": "<EMAIL>",
        "name": "Employee User",
        "status": "active",
        "avatar": "https://minio-api.atharyouth.org/uploads/123abc456xyz",
        "user_roles": [
          {
            "id": 1
            "is_default": true,
            "role": {
              "id": "6",
              "name": "employee",
              "global": false
            },
            "project": {
              "id": "1",
              "name": "Project Alpha"
            }
          }
        ]
      }
    },
    "meta": {
      "systems": [
        {
          "name": "people",
          "alias": "people",
          "display_name": "AtharPeople",
          "description": "People management system"
        },
        {
          "name": "procure",
          "alias": "procure",
          "display_name": "AtharProcure",
          "description": "Procurement system"
        },
        {
          "name": "case_manager",
          "alias": "cm",
          "display_name": "AtharCaseManager",
          "description": "Case management system"
        }
      ]
    }
  }
  JSON

  example <<-JSON
  {
    "error": "Expired access token"
  }
  JSON

  def me
    return unless authorize!(:read, :user)

    user = User.find(current_user.id)

    # Add systems information as meta data
    options = {
      meta: {
        allowed_systems: [
          { name: "people", alias: "people", display_name: "AtharPeople", description: "People management system" },
          { name: "procure", alias: "procure", display_name: "AtharProcure", description: "Procurement system" },
          { name: "case_manager", alias: "cm", display_name: "AtharCM", description: "Case management system" }
        ]
      }
    }

    render json: UserSerializer.new(user, options).serializable_hash.to_json, status: :ok
  end

  api :PATCH, "/users/me", "Updates the authenticated user's basic information and profile image"
  header "Authorization", "Scoped session token as Bearer token", required: true
  formats [ "multipart/form-data" ]
  param :name, String, desc: "Full name of the user"
  param :email, String, desc: "User email address"
  param :avatar, ActionDispatch::Http::UploadedFile, desc: "The profile image file to be uploaded (optional)"
  description <<-HTML
    Updates the authenticated user's basic information and optionally the profile image.
    <br/>
    Requires a valid scoped session token.
    <br/><br/>
    <b>Permission required:</b> <code>:update, :user</code>
    <br/><br/>
    Returns the updated user data in JSON:API format.
  HTML
  returns code: 200, desc: "User updated successfully" do
    property :data, Hash, desc: "JSON:API data object" do
      property :id, String, desc: "User ID"
      property :type, String, desc: "Resource type (user)"
      property :attributes, Hash, desc: "User attributes" do
        property :email, String, desc: "User email"
        property :name, String, desc: "User's full name"
        property :status, String, desc: "User status (active/inactive)"
        property :avatar, String, desc: "CDN URL of the profile image (if available)"
        property :user_roles, Array, desc: "Array of user roles with role and project details"
      end
    end
  end
  error code: 422, desc: "Validation failed"
  example <<-JSON
  {
    "data": {
      "id": "5",
      "type": "user",
      "attributes": {
        "email": "<EMAIL>",
        "name": "Updated Name",
        "status": "active",
        "avatar": "https://minio-api.atharyouth.org/uploads/123abc456xyz",
        "user_roles": [
          {
            "id": 1,
            "is_default": true,
            "role": {
              "id": 6,
              "name": "employee",
              "global": false
            },
            "project": {
              "id": 1,
              "name": "Project Alpha",
              "description": "First test project",
              "status": "active"
            }
          }
        ]
      }
    }
  }
  JSON

  def update
    return unless authorize!(:update, :user)

    user = User.find(current_user.id)

    # Handle profile image if present
    if params[:avatar].present?
      user.avatar.attach(
        io: params[:avatar],
        filename: params[:avatar].original_filename,
        content_type: params[:avatar].content_type
      )
      user.avatar.reload
    end

    if user.update(user_params)
      render json: UserSerializer.new(user).serializable_hash.to_json, status: :ok
    else
      render json: { error: user.errors.full_messages }, status: :unprocessable_entity
    end
  end

  api :PATCH, "/users/me/password", "Changes the user's password"
  header "Authorization", "Scoped session token as Bearer token", required: true
  formats [ "json" ]
  param :current_password, String, required: true, desc: "User's current password"
  param :password, String, required: true, desc: "New password"
  param :password_confirmation, String, required: true, desc: "Confirmation of the new password"

  description <<-HTML
    Changes the authenticated user's password.

    This action requires a valid <b>scoped session token</b>.

    The token must be scoped to one of the subsystems: <code>core</code>, <code>people</code>, <code>case_manager</code>, or <code>procure</code>.

    <b>Permission required:</b> <code>:update, :user</code>
  HTML

  returns code: 200, desc: "Password changed successfully" do
    property :data, Hash, desc: "JSON:API data object" do
      property :id, String, desc: "User ID"
      property :type, String, desc: "Resource type (user)"
      property :attributes, Hash, desc: "User attributes" do
        property :email, String, desc: "User email"
        property :name, String, desc: "User's full name"
        property :status, String, desc: "User status (active/inactive)"
        property :avatar, String, desc: "CDN URL of the profile image (if available)"
        property :user_roles, Array, desc: "Array of user roles with role and project details"
      end
    end
    property :meta, Hash, desc: "Metadata" do
      property :message, String, desc: "Success message"
    end
  end

  error code: 422, desc: "Validation failed or current password is incorrect"

  example <<-JSON
  {
    "data": {
      "id": "5",
      "type": "user",
      "attributes": {
        "email": "<EMAIL>",
        "name": "Employee User",
        "status": "active",
        "avatar": "https://minio-api.atharyouth.org/uploads/123abc456xyz",
        "user_roles": [
          {
            "id": 1,
            "is_default": true,
            "role": {
              "id": 6,
              "name": "employee",
              "global": false
            },
            "project": {
              "id": 1,
              "name": "Project Alpha",
              "description": "First test project",
              "status": "active"
            }
          }
        ]
      }
    },
    "meta": {
      "message": "Password changed successfully."
    }
  }
  JSON

  example <<-JSON
  {
    "error": ["Current password is incorrect"]
  }
  JSON

  def change_password
    return unless authorize!(:update, :user)

    user = User.find(current_user.id)

    # Check if current password is correct
    unless user.valid_password?(params[:current_password])
      return render json: { error: [ "Current password is incorrect" ] }, status: :unprocessable_entity
    end

    # Check if password and confirmation match
    if params[:password] != params[:password_confirmation]
      return render json: { error: [ "Password confirmation doesn't match Password" ] }, status: :unprocessable_entity
    end

    # Update the password
    if user.update(password: params[:password])
      # Create a response with message and serialized user data
      options = {
        meta: {
          message: "Password changed successfully."
        }
      }

      render json: UserSerializer.new(user, options).serializable_hash.to_json, status: :ok
    else
      render json: { error: user.errors.full_messages }, status: :unprocessable_entity
    end
  end

  api :GET, "/users/me/permissions", "Returns the currently authenticated user's permissions"
  header "Authorization", "Scoped session token as Bearer token", required: true
  description <<-HTML
    Returns the authenticated user's permissions for the current subsystem scope.

    Requires a valid <b>scoped session token</b> issued via <code>/auth/session_token</code>.
    Token must be scoped to one of the subsystems: <code>core</code>, <code>people</code>, <code>case_manager</code>, or <code>procure</code>.

    <b>Permission required:</b> <code>:read, :user</code>
  HTML
  returns code: 200, desc: "User permissions for the current subsystem" do
    property :data, Array, desc: "Array of permission objects" do
      property :id, String, desc: "Permission identifier in format 'action:subject'"
      property :type, String, desc: "Resource type (always 'permission')"
      property :attributes, Hash, desc: "Permission attributes" do
        property :action, String, desc: "The action part of the permission (e.g., 'read', 'update')"
        property :subject, String, desc: "The subject part of the permission (e.g., 'user', 'project')"
        property :permission, String, desc: "Full permission string in format 'action:subject'"
      end
    end
    property :meta, Hash, desc: "Metadata" do
      property :scope, String, desc: "Current subsystem scope"
      property :count, Integer, desc: "Number of permissions"
    end
  end
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"
  example <<-JSON
  {
    "data": [
      {
        "id": "read:user",
        "type": "permission",
        "attributes": {
          "action": "read",
          "subject": "user",
          "permission": "read:user"
        }
      }
    ],
    "meta": {
      "scope": "core",
      "count": 4
    }
  }
  JSON

  def permissions
    return unless authorize!(:read, :user)

    # Get the current scope from the token
    scope = decoded_token&.dig("scope") || "unknown"

    # Create a response that better follows JSON:API format
    response_data = @permissions.map do |permission|
      action, subject = permission.split(':')
      {
        id: permission,
        type: 'permission',
        attributes: {
          action: action,
          subject: subject,
          permission: permission
        }
      }
    end

    render json: {
      data: response_data,
      meta: {
        scope: scope,
        count: @permissions.size
      }
    }, status: :ok
  end

  private

  def set_permissions
    @permissions = Permission.for_user(current_user)
                             .by_system(params[:scope] || "core")
                             .map(&:to_s)
  end

  def user_params
    params.permit(:name, :email)
  end
end
